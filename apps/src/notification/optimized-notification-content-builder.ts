import { Injectable } from '@nestjs/common';
import { DirectSalesforceService } from '../common/direct-salesforce.service';
import { DynamoDBService } from '../common/dynamodb.service';
import { S3Service } from '../common/s3.service';
import { CobrandingService } from '../cobranding/service';
import { LoggerService } from '../common/cloudwatchService';
import { LoggerEnum } from '@gus-eip/loggers';
import { AuthService } from '../common/auth.service';

const loggerEnum = new LoggerEnum();

@Injectable()
export class OptimizedNotificationContentBuilder {
  constructor(
    private readonly directSalesforceService: DirectSalesforceService,
    private readonly dynamoDBService: DynamoDBService,
    private readonly s3Service: S3Service,
    private readonly cobrandingService: CobrandingService,
    private readonly loggerService: LoggerService,
    private readonly authService: AuthService,
  ) {}

  /**
   * Optimized method for WELCOME_STUDENT event that uses direct Salesforce calls
   * instead of middleware service calls
   */
  async refreshOpportunityFromSalesforceOptimized(
    email: string,
    opportunityId: string,
  ): Promise<any> {
    try {
      if (!opportunityId || !email) {
        console.log('Missing email or opportunityId');
        return;
      }

      // Check if the opportunity exists in DynamoDB
      const existingOpportunity = await this.dynamoDBService.getObject(
        process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        {
          PK: email,
          SK: opportunityId,
        },
      );

      if (!existingOpportunity.Item) {
        console.log(
          `No opportunity found for email ${email} and Id ${opportunityId} in DB`,
        );
      }

      // Fetch the latest data from Salesforce using direct API call
      console.log('Fetching latest data from Salesforce using direct API...');
      const response = await this.directSalesforceService.getOpportunityById(
        opportunityId,
      );

      const opportunityDetails = response.records?.[0];
      if (!opportunityDetails) {
        console.log(
          `No opportunity found in Salesforce for Id - ${opportunityId}`,
        );
        return false;
      }

      // Update the opportunity in DynamoDB
      console.log('Updating opportunity in DynamoDB...');
      const params = {
        Item: {
          PK: email,
          SK: opportunityId,
          updatedAt: new Date().toISOString(),
          ...opportunityDetails,
        },
      };

      await this.dynamoDBService.putObject(
        process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        params,
      );

      console.log(
        'Opportunity refreshed successfully in DynamoDB using direct SF API',
      );
      return true;
    } catch (error) {
      console.log('Error when refreshing opportunity details from SF:', error);
      throw error;
    }
  }

  /**
   * Optimized processing for WELCOME_STUDENT events
   */
  async processWelcomeStudentOptimized(payload: any): Promise<void> {
    try {
      console.log(
        'Processing WELCOME_STUDENT event with optimized direct SF calls',
      );

      // Refresh opportunity from Salesforce using direct API
      await this.refreshOpportunityFromSalesforceOptimized(
        payload.email,
        payload.messageDetails.opportunityId,
      );

      console.log(
        'WELCOME_STUDENT event processed successfully with direct SF calls',
      );
    } catch (error) {
      console.log('Error processing WELCOME_STUDENT event:', error);
      throw error;
    }
  }

  /**
   * Log method for tracking optimized operations
   */
  async log(
    correlationId: string,
    source: string,
    destination: string,
    event: string,
    usecase: string,
    message: string,
    secondaryKey?: string,
    entityKey?: string,
    entityKeyField?: string,
    sourcePayload?: any,
    destinationPayload?: any,
    brand?: string,
  ) {
    await this.loggerService.log(
      correlationId,
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      source,
      destination,
      event,
      usecase,
      sourcePayload || {},
      destinationPayload || {},
      message,
      brand || 'Apphero',
      secondaryKey || '',
      entityKeyField,
      entityKey,
    );
  }

  /**
   * Error logging method for optimized operations
   */
  async error(
    correlationId: string,
    source: string,
    destination: string,
    event: string,
    usecase: string,
    errorMessage: string,
    secondaryKey?: string,
    entityKey?: string,
    entityKeyField?: string,
    sourcePayload?: any,
    destinationPayload?: any,
    brand?: string,
  ) {
    await this.loggerService.error(
      correlationId,
      new Date().toISOString(),
      loggerEnum.Component.APPHERO_BACKEND,
      source,
      destination,
      event,
      usecase,
      sourcePayload || {},
      destinationPayload || {},
      errorMessage,
      brand || 'Apphero',
      secondaryKey || '',
      entityKeyField,
      entityKey,
    );
  }

  /**
   * Optimized method to get opportunities by email using direct Salesforce calls
   */
  async getOpportunitiesByEmailOptimized(email: string): Promise<any> {
    try {
      console.log('Fetching opportunities by email using direct SF API...');
      const response =
        await this.directSalesforceService.getOpportunitiesByEmail(email);

      if (!response.records || response.records.length === 0) {
        console.log(`No opportunities found for email: ${email}`);
        return { response: [] };
      }

      // Map the response to match the expected format
      const mappedOpportunities = response.records.map(
        (opportunityDetails) => ({
          PK: opportunityDetails?.Account?.PersonEmail,
          SK: opportunityDetails.Id,
          createdAt: new Date().toISOString(),
          ...opportunityDetails,
        }),
      );

      // Save to DynamoDB for future use
      if (mappedOpportunities.length > 0) {
        await this.dynamoDBService.uploadBulkDataToDynamoDB(
          mappedOpportunities,
          process.env.APPHERO_SF_OPPORTUNITY_TABLE,
        );
      }

      console.log(
        `Found ${mappedOpportunities.length} opportunities using direct SF API`,
      );
      return { response: mappedOpportunities };
    } catch (error) {
      console.log(
        'Error fetching opportunities by email using direct SF API:',
        error,
      );
      throw error;
    }
  }

  /**
   * Optimized method to get opportunity by ID and email using direct Salesforce calls
   */
  async getOpportunityByIdAndEmailOptimized(
    opportunityId: string,
    email?: string,
  ): Promise<any> {
    try {
      console.log('Fetching opportunity by ID using direct SF API...');
      const response = await this.directSalesforceService.getOpportunityById(
        opportunityId,
      );

      if (!response.records || response.records.length === 0) {
        console.log(`No opportunity found for ID: ${opportunityId}`);
        return { response: [] };
      }

      const opportunityDetails = response.records[0];

      // Save to DynamoDB for future use
      if (opportunityDetails && email) {
        const params = {
          Item: {
            PK: email,
            SK: opportunityId,
            createdAt: new Date().toISOString(),
            ...opportunityDetails,
          },
        };
        await this.dynamoDBService.putObject(
          process.env.APPHERO_SF_OPPORTUNITY_TABLE,
          params,
        );
      }

      console.log('Opportunity fetched successfully using direct SF API');
      return { response: [opportunityDetails] };
    } catch (error) {
      console.log(
        'Error fetching opportunity by ID using direct SF API:',
        error,
      );
      throw error;
    }
  }

  /**
   * Optimized method to get additional info using direct Salesforce calls
   */
  async getAdditionalInfoOptimized(
    messageDetails: any,
    email: string,
    event: any,
  ): Promise<any> {
    try {
      console.log('Getting additional info using optimized direct SF calls...');

      // Get opportunity details using direct SF API
      const opportunityRequest = await this.getOpportunityByIdAndEmailOptimized(
        messageDetails.opportunityId,
        email,
      );
      const opportunityDetails = opportunityRequest?.response[0];

      if (!opportunityDetails) {
        throw new Error(
          `No opportunity found for ID: ${messageDetails.opportunityId}`,
        );
      }

      console.log('opportunityDetails from direct SF API:', opportunityDetails);
      const currentStage = opportunityDetails.ApplicationStatus;

      // Get opportunities by email using direct SF API
      let opportunitiesResponse = await this.getOpportunitiesByEmailOptimized(
        email,
      );
      const filteredOpportunities = opportunitiesResponse.response.filter(
        (opportunity) => opportunity?.Id !== messageDetails.opportunityId,
      );

      const applicationCount = filteredOpportunities?.length;
      let brand =
        event === 'WELCOME_STUDENT' && applicationCount >= 1
          ? 'APPHERO'
          : messageDetails.brand;

      console.log('Filtered Opportunities Count:', applicationCount);
      let customEvent;
      if (event === 'APPLICATION_SUBMITTED') {
        if (await this.authService.checkEmailExistInCognito(email)) {
          customEvent = 'APPLICATION_SUBMITTED_TRACK_NEW_APPLICATION';
        } else {
          customEvent = 'APPLICATION_SUBMITTED_JOIN_APPHERO';
        }
      }

      if (event === 'DRAFT_APPLICATION') {
        if (await this.authService.checkEmailExistInCognito(email)) {
          customEvent = 'DRAFT_APPLICATION_EXISTING_USER';
        } else {
          customEvent = 'DRAFT_APPLICATION_NEW_USER';
        }
      }

      const additionalInfo = {
        programName:
          opportunityDetails?.OpportunityLineItems?.records[0]?.Product2
            ?.ProgrammeName__c,
        institution: opportunityDetails?.Institution_Full_Name__c,
        personAccountName: opportunityDetails?.Account?.Name,
        agentAccountName: opportunityDetails?.AgentAccountName__c,
        agentContactName: opportunityDetails?.Agent_Contact__r?.Name,
        currentStage: currentStage?.status,
        buttonUrl: `${process.env.APPHERO_APP_ENDPOINT}/view-application-details?id=${opportunityDetails?.Id}`,
        studentBrand: brand,
        event: customEvent ? customEvent : event,
        visaRecords: opportunityDetails?.Visa_Application__r?.records,
        advisorName:
          opportunityDetails?.ApplicationSource__c === 'Agent Portal' ||
          opportunityDetails?.ApplicationSource__c === 'Portal uLink'
            ? opportunityDetails?.AgentAccountName__c
            : opportunityDetails.OwnerName__c,
      };

      console.log('Additional info retrieved using direct SF API');
      return additionalInfo;
    } catch (error) {
      console.log('Error getting additional info using direct SF API:', error);
      throw error;
    }
  }
}
