import { forwardRef, Module } from '@nestjs/common';
import { NotificaitonController } from './controller';
import { CommonModule } from '../common/module';
import { NotificationsService } from './notification.service';
import { AdmissionStatusUpdateContent } from './notificationContentBuilder/notificationEventContent/admissionUpdateNotification';
import { ClosedTask } from './notificationContentBuilder/notificationEventContent/closedTask';
import { NotificationContentBuilder } from './notificationContentBuilder/notificationContentBuilder';
import { WelcomeStudentContent } from './notificationContentBuilder/notificationEventContent/welcomeStudent';
import { ReviewCenterContent } from './notificationContentBuilder/notificationEventContent/reviewCenterComment';
import { AdmissionConditionContent } from './notificationContentBuilder/notificationEventContent/admissionCondition';
import { IssuedLetterContent } from './notificationContentBuilder/notificationEventContent/issuedLetter';
import { AgentUploadDocumentContent } from './notificationContentBuilder/notificationEventContent/agentUploadDocument';
import { OpportunityModule } from 'apps/src/opportunity/module';
import { CobrandingModule } from '../cobranding/module';
import { JoinAppheroContent } from './notificationContentBuilder/notificationEventContent/joinApphero';
import { TrackNewApplicationContent } from './notificationContentBuilder/notificationEventContent/trackNewApplication';
import { AdmissionLetterDeletion } from './notificationContentBuilder/notificationEventContent/admissionLetterDeletion';
import { TaskReopen } from './notificationContentBuilder/notificationEventContent/taskReopen';
import { OpportunityUpdateContent } from './notificationContentBuilder/notificationEventContent/opportunityUpdateContent';
import { VisaContent } from './notificationContentBuilder/notificationEventContent/visa';
import { IncorrectLoginContent } from './notificationContentBuilder/notificationEventContent/incorrectLogin';
import { UnconfirmedUserContent } from './notificationContentBuilder/notificationEventContent/unconfirmedUser';
import { UserNotConfirmedContent } from './notificationContentBuilder/notificationEventContent/userNotConfirmed';
import { SupportCaseContent } from './notificationContentBuilder/notificationEventContent/supportCaseContent';
import { OptimizedNotificationContentBuilder } from './optimized-notification-content-builder';
import { DraftApplicationExistingUserContent } from './notificationContentBuilder/notificationEventContent/draftApplicationExistingUser';
import { DraftApplicationNewUserContent } from './notificationContentBuilder/notificationEventContent/draftApplicationNewUser';
@Module({
  imports: [
    CommonModule,
    forwardRef(() => OpportunityModule),
    CobrandingModule,
  ],
  controllers: [NotificaitonController],
  providers: [
    NotificaitonController,
    NotificationsService,
    AdmissionStatusUpdateContent,
    NotificationContentBuilder,
    OptimizedNotificationContentBuilder,
    WelcomeStudentContent,
    ReviewCenterContent,
    AdmissionConditionContent,
    IssuedLetterContent,
    AgentUploadDocumentContent,
    OpportunityUpdateContent,
    ClosedTask,
    JoinAppheroContent,
    TrackNewApplicationContent,
    AdmissionLetterDeletion,
    TaskReopen,
    VisaContent,
    IncorrectLoginContent,
    UnconfirmedUserContent,
    UserNotConfirmedContent,
    SupportCaseContent,
    DraftApplicationExistingUserContent,
    DraftApplicationNewUserContent,
  ],
  exports: [
    NotificaitonController,
    NotificationContentBuilder,
    NotificationsService,
  ],
})
export class NotificationModule {}
